
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Row, Col } from 'antd';

const { Option } = Select;

const InfoFilter = ({ curTask, sceneList, reportTaskOption, onOptionChange, reportNames, onAddData, loading }) => {
    // 构建用例选项（参考 FramePage 的实现）
    const caseOptions = (curTask?.planParams?.caseNodeParams?.caseNodeList || []).map(
        (item) => (
            <Option key={item?.caseNodeId} value={item?.caseNodeId}>
                {item?.caseNodeName}
            </Option>
        )
    );

    // 构建场景选项
    const sceneOptions = (sceneList || []).map((item) => (
        <Option key={item?.id} value={item?.id}>
            {item?.name}
        </Option>
    ));

    // 构建模块选项
    const moduleOptions = reportNames.map((name, index) => (
        <Option key={name} value={index}>
            {name}
        </Option>
    ));

    return (
        <Form
            style={{
                width: '100%',
                paddingTop: '16px',
                borderTop: '1px solid #f5f5f5'
            }}
        >
            <Row>
                <Col span={6}>
                    <Form.Item label="执行用例" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.caseId}
                            placeholder="请选择执行用例"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    caseId: value,
                                    sceneId: null
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {caseOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={6} offset={1}>
                    <Form.Item label="执行场景" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.sceneId}
                            placeholder="请选择执行场景"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    sceneId: value
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {sceneOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={6} offset={1}>
                    <Form.Item label="报告模块" required style={{ width: '100%' }}>
                        <Select
                            value={reportTaskOption.modalIndex}
                            placeholder="请选择报告模块"
                            onChange={(value) => {
                                const newOption = {
                                    ...reportTaskOption,
                                    modalIndex: value
                                };
                                onOptionChange(newOption);
                            }}
                        >
                            {moduleOptions}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={3}>
                    <Form.Item>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={onAddData}
                        >
                            添加至报告
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    );
};

export default InfoFilter;
