
.frame_li {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    cursor: pointer;
    flex-shrink: 0;
}

.frame_li:first-child {
    margin-left: 5px;
}



.slider_container {
    width: 80%;
    margin-left: 10%;
    padding: 10px 5px;
    margin-top: 10px;
    margin-bottom: 10px;
}


.main_container {
    width: 100%;
    margin-top: -10px;
    background-color: #fff;
}


.empty_container {
    text-align: center;
    padding: 50px;
    color: #999;
}


.frame_section {
    display: flex;
    align-items: flex-start;
    width: 100%;
    min-height: 280px;
    margin-top: 0;

    &.last_section {
        margin-top: 3px;
    }
}

// 控制按钮容器
.control_container {
    position: relative;
    width: 40px;
    min-height: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

// 控制按钮样式
.control_button {
    font-size: 20px;
    cursor: pointer;

    &.stage_button {
        margin-bottom: 10px;
    }
}

// 帧列表容器
.frame_list_container {
    flex: 1;
    min-width: 0;
}

// 帧列表样式
.frame_list {
    width: 100%;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    list-style: none;

    &.inactive {
        opacity: 0.2;
    }
}

// 帧图片容器
.frame_image_container {
    position: relative;
}

// 帧标签样式
.frame_label {
    text-align: center;
    width: 100%;
    z-index: 998;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    position: absolute;
    bottom: 0;
}

// 时间戳容器
.timestamp_container {
    display: flex;
    margin-top: 5px;
    justify-content: center;
}

// 预览按钮样式
.preview_button {
    position: absolute;
    bottom: 35px;
}

// 帧图片样式
.frame_image {
    object-fit: cover;
}
