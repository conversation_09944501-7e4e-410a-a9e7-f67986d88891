.slideCalibrate {
    width: 100%;
    height: 100%;
    
    .calibrateCard {
        height: 100%;
        
        :global(.ant-card-body) {
            height: calc(100% - 57px);
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    
    .paramInfo {
        text-align: left;
        background: #f5f5f5;
        padding: 16px;
        border-radius: 6px;
        margin-top: 16px;
        
        p {
            margin: 4px 0;
            color: #666;
            
            &:first-child {
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
        }
    }
}
