import React from 'react';
import { Card, Result } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import styles from './SlideCalibrate.module.less';

const SlideCalibrate = ({ planId, caseNodeId, sceneId }) => {
    return (
        <div className={styles.slideCalibrate}>
            <Card className={styles.calibrateCard}>
                <Result
                    icon={<AppstoreOutlined />}
                    title="滑动校准"
                    subTitle="滑动校准功能正在开发中，敬请期待..."
                    extra={
                        <div className={styles.paramInfo}>
                            <p>当前参数:</p>
                            <p>计划ID: {planId || '未选择'}</p>
                            <p>用例节点ID: {caseNodeId || '未选择'}</p>
                            <p>场景ID: {sceneId || '未选择'}</p>
                        </div>
                    }
                />
            </Card>
        </div>
    );
};

export default SlideCalibrate;
